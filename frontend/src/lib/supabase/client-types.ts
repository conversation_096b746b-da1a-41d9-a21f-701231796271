// frontend/src/lib/supabase/client-types.ts

import type { SupabaseClient } from '@supabase/supabase-js';
import type { PostgrestFilterBuilder, PostgrestQueryBuilder } from '@supabase/postgrest-js';
import { PostgrestSingleResponse, PostgrestMaybeSingleResponse, PostgrestResponse } from '@supabase/supabase-js';
import type { Database } from './database.types';

/**
 * Extended type for Supabase client that properly includes schema methods
 * and other extended functionality that the TypeScript types may not fully capture.
 *
 * This type ensures proper TypeScript support for schema-specific operations
 * without changing any runtime behavior.
 */
export type TypedSupabaseClient = SupabaseClient<Database> & {
  /**
   * Set the schema to use for subsequent operations
   * @param schema The schema name (e.g., 'tenants', 'public')
   */
  schema: (schema: string) => TypedSupabaseClient;

  /**
   * Select a table from the database, with improved type definitions
   * This overload ensures proper typing for schema-specific tables
   * @param relation The table name
   */
  from: <T extends keyof Database | string>(
    relation: T
  ) => PostgrestQueryBuilder<any, any, any> & {
    // Add missing methods that exist at runtime but are not in the TypeScript types
    groupBy: (columns: string | string[]) => PostgrestFilterBuilder<any, any, any, any> & {
      // And ensure groupBy returns an object that also has the expected methods
      select: (columns: string) => PostgrestFilterBuilder<any, any, any, any>;
    };
  }

  /**
   * Create a helper to support tenant operations more explicitly and safely
   * @param tenantId The tenant ID to filter by
   */
  forTenant: (tenantId: string) => {
    /**
     * Set schema context with tenant ID pre-configured
     * @param schema The schema name
     */
    schema: (schema: string) => TypedSupabaseClient & {
      /**
       * Apply tenant filter automatically to table operations
       * @param tableName The table name
       */
      withTenantFilter: <T>(tableName: string) => any; // Using any for PostgrestFilterBuilder to avoid complex generic parameters
    }
  };
};
