{"extends": "../tsconfig.base.json", "compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "jsx": "preserve", "jsxImportSource": "react", "incremental": true, "baseUrl": ".", "ignoreDeprecations": "5.0", "typeRoots": ["./node_modules/@types", "../node_modules/@types"], "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/app/*": ["./src/app/*"], "@/contexts/*": ["./src/contexts/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts", "src/types/**/*.d.ts"], "exclude": ["node_modules", ".next", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/*.cy.ts", "**/*.cy.tsx", "__tests__/**/*", "cypress/**/*"]}