{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "react-jsx", "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": false, "baseUrl": ".", "paths": {"@/*": ["./frontend/src/*"], "@/components/*": ["./frontend/src/components/*"], "@/lib/*": ["./frontend/src/lib/*"], "@/hooks/*": ["./frontend/src/hooks/*"], "@/types/*": ["./frontend/src/types/*"], "@/app/*": ["./frontend/src/app/*"], "@/contexts/*": ["./frontend/src/contexts/*"], "@ailex/*": ["./packages/*/src/*"]}}}