name: Diag PNPM

on:
  push:
    branches: [ stripe, develop, main ]
  pull_request:
    branches: [ stripe, develop, main ]

jobs:
  diag:
    runs-on: ubuntu-latest
    steps:
      - name: Runner info
        run: |
          uname -a
          cat /etc/os-release
          echo "SHELL=$SHELL"
          env | sort | sed -n '1,120p'
          
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          check-latest: true
          
      - name: Enable Corepack + pnpm
        run: |
          corepack enable
          corepack prepare pnpm@latest --activate
          which pnpm || true
          pnpm -v || true
          
      - name: Smoke-test pnpm (no repo deps)
        run: |
          mkdir -p /tmp/pnpm-smoke && cd /tmp/pnpm-smoke
          printf '{ "name": "smoke", "version": "0.0.0" }\n' > package.json
          pnpm install --frozen-lockfile || true
          echo "STORE: $(pnpm store path || true)"
          
      - name: Upload pnpm debug log (if exists)
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: pnpm-debug
          path: |
            ~/.pnpm-debug.log
            /tmp/pnpm-smoke/pnpm-debug.log
          if-no-files-found: ignore
