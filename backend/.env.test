# Test environment variables
MCP_RULES_BASE=/tmp/mcp_rules
STRIPE_SECRET_KEY=sk_test_dummy_key_for_testing
STRIPE_WEBHOOK_SECRET=whsec_test_secret_key_for_testing
OPENAI_API_KEY=test-dummy-key
ANTHROPIC_API_KEY=test-dummy-key
DATABASE_URL=postgresql://localhost/test_db
SUPABASE_URL=https://test.supabase.co
SUPABASE_ANON_KEY=test-anon-key
SUPABASE_SERVICE_ROLE_KEY=test-service-key
REDIS_URL=redis://localhost:6379/0
CALENDLY_PAT=test-calendly-pat
CALENDLY_ORG_URI=test-calendly-org
AUTH_SERVICE_URL=http://localhost:8000
TWILIO_ACCOUNT_SID=test-twilio-sid
TWILIO_AUTH_TOKEN=test-twilio-token
TWILIO_PHONE_NUMBER=+**********
RESEND_API_KEY=test-resend-key
ENVIRONMENT=test
DEBUG=True