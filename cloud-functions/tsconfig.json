{"extends": "../tsconfig.base.json", "compilerOptions": {"module": "commonjs", "moduleResolution": "node", "target": "es2017", "noImplicitReturns": true, "noUnusedLocals": true, "outDir": "lib", "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true}, "compileOnSave": true, "include": ["*.ts"], "exclude": ["node_modules", "lib", "**/*.test.ts", "**/*.spec.ts"]}