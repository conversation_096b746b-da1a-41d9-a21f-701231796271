{"extends": "../../tsconfig.base.json", "compilerOptions": {"moduleResolution": "node", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}