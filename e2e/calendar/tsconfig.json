{"extends": "../../tsconfig.base.json", "compilerOptions": {"lib": ["ES2020", "DOM"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./*"]}, "types": ["node", "@playwright/test"]}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "test-results"]}